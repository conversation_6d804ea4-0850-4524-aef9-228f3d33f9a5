import { defineStore } from 'pinia'

export const useTagsViewStore = defineStore(
  'tagsView',
  {
    state: () => ({
      visitedViews: [],
      cachedViews: []
    }),
    actions: {
      addVisitedView(view) {
        if (this.visitedViews.some(v => v.path === view.path)) return
        this.visitedViews.push(
          Object.assign({}, view, {
            title: view.meta.title || 'no-name'
          })
        )
      },
      addCachedView(view) {
        if (this.cachedViews.includes(view.name)) return
        if (!view.meta.noCache) {
          this.cachedViews.push(view.name)
        }
      },
      delVisitedView(view) {
        for (const [i, v] of this.visitedViews.entries()) {
          if (v.path === view.path) {
            this.visitedViews.splice(i, 1)
            break
          }
        }
      },
      delCachedView(view) {
        const index = this.cachedViews.indexOf(view.name)
        index > -1 && this.cachedViews.splice(index, 1)
      },
      delOthersVisitedViews(view) {
        this.visitedViews = this.visitedViews.filter(v => v.path === view.path)
      },
      delOthersCachedViews(view) {
        const index = this.cachedViews.indexOf(view.name)
        if (index > -1) {
          this.cachedViews = this.cachedViews.slice(index, index + 1)
        } else {
          // if index = -1, there is no cached tags
          this.cachedViews = []
        }
      },
      delAllVisitedViews() {
        this.visitedViews = []
      },
      delAllCachedViews() {
        this.cachedViews = []
      },
      updateVisitedView(view) {
        for (let v of this.visitedViews) {
          if (v.path === view.path) {
            v = Object.assign(v, view)
            break
          }
        }
      }
    }
  })
