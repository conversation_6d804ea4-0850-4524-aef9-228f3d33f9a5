"""
角色和权限常量定义
"""

class RoleConstants:
    """角色常量"""
    SUPER_ADMIN = 'admin'           # 超级管理员 (沿用现有)
    PROJECT_MANAGER = 'project_manager'  # 项目管理员  
    PROJECT_MEMBER = 'project_member'    # 项目成员
    NORMAL_USER = 'normal_user'          # 普通用户

class PermissionConstants:
    """权限常量"""
    # 超级管理员权限
    ALL_PERMISSION = '*:*:*'
    
    # 菜单级权限
    MENU_HOME = 'menu:home'               # 首页
    MENU_TOOLS = 'menu:tools'             # 工具广场
    MENU_PROJECT = 'menu:project'         # 项目管理
    MENU_DATABASE = 'menu:database'       # 数据库管理
    
    # 项目管理权限
    PROJECT_LIST = 'project:list'         # 查看项目列表
    PROJECT_CREATE = 'project:create'     # 创建项目
    PROJECT_EDIT = 'project:edit'         # 编辑项目
    PROJECT_DELETE = 'project:delete'     # 删除项目
    PROJECT_MEMBER_MANAGE = 'project:member:manage'  # 管理项目成员
    
    # 任务管理权限
    TASK_LIST = 'task:list'               # 查看任务列表
    TASK_CREATE = 'task:create'           # 创建任务
    TASK_EDIT_OWN = 'task:edit:own'       # 编辑自己的任务
    TASK_EDIT_ALL = 'task:edit:all'       # 编辑所有任务
    TASK_DELETE_OWN = 'task:delete:own'   # 删除自己的任务
    TASK_DELETE_ALL = 'task:delete:all'   # 删除所有任务
    TASK_EXECUTE = 'task:execute'         # 执行任务
    TASK_DOWNLOAD = 'task:download'       # 下载任务结果
    
    # 数据库管理权限
    DATABASE_LIST = 'database:list'       # 查看数据库列表
    DATABASE_CREATE = 'database:create'   # 创建数据库
    DATABASE_EDIT = 'database:edit'       # 编辑数据库
    DATABASE_DELETE = 'database:delete'   # 删除数据库

# 角色权限映射表
ROLE_PERMISSIONS = {
    RoleConstants.SUPER_ADMIN: [
        PermissionConstants.ALL_PERMISSION
    ],
    
    RoleConstants.PROJECT_MANAGER: [
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
        PermissionConstants.MENU_PROJECT,
        PermissionConstants.MENU_DATABASE,
        PermissionConstants.PROJECT_LIST,
        PermissionConstants.PROJECT_CREATE,
        PermissionConstants.PROJECT_EDIT,
        PermissionConstants.PROJECT_DELETE,
        PermissionConstants.PROJECT_MEMBER_MANAGE,
        PermissionConstants.TASK_LIST,
        PermissionConstants.TASK_CREATE,
        PermissionConstants.TASK_EDIT_ALL,
        PermissionConstants.TASK_DELETE_ALL,
        PermissionConstants.TASK_EXECUTE,
        PermissionConstants.TASK_DOWNLOAD,
        PermissionConstants.DATABASE_LIST,
        PermissionConstants.DATABASE_CREATE,
        PermissionConstants.DATABASE_EDIT,
        PermissionConstants.DATABASE_DELETE,
    ],
    
    RoleConstants.PROJECT_MEMBER: [
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
        PermissionConstants.MENU_PROJECT,
        PermissionConstants.PROJECT_LIST,
        PermissionConstants.TASK_LIST,
        PermissionConstants.TASK_CREATE,
        PermissionConstants.TASK_EDIT_OWN,
        PermissionConstants.TASK_DELETE_OWN,
        PermissionConstants.TASK_EXECUTE,
        PermissionConstants.TASK_DOWNLOAD,
    ],
    
    RoleConstants.NORMAL_USER: [
        PermissionConstants.MENU_HOME,
        PermissionConstants.MENU_TOOLS,
    ]
}