"""
权限初始化脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.permission_service import PermissionService
from utils.log_util import logger

async def init_role_permissions():
    """初始化角色权限"""
    try:
        # 获取数据库会话
        db_gen = get_db()
        db: AsyncSession = next(db_gen)
        
        logger.info("开始初始化角色权限...")
        
        # 初始化所有角色权限
        success = await PermissionService.init_all_role_permissions(db)
        
        if success:
            logger.info("角色权限初始化成功！")
            print("✅ 角色权限初始化成功！")
        else:
            logger.error("角色权限初始化失败！")
            print("❌ 角色权限初始化失败！")
            
        await db.close()
        return success
        
    except Exception as e:
        logger.error(f"权限初始化异常: {e}")
        print(f"❌ 权限初始化异常: {e}")
        return False

async def check_permissions():
    """检查权限配置"""
    try:
        # 获取数据库会话
        db_gen = get_db()
        db: AsyncSession = next(db_gen)
        
        print("\n📋 权限配置检查:")
        
        # 获取角色权限配置
        role_permissions = PermissionService.get_role_permissions_config()
        
        for role, permissions in role_permissions.items():
            print(f"\n🔑 角色: {role}")
            print(f"   权限数量: {len(permissions)}")
            if len(permissions) <= 5:
                for perm in permissions:
                    print(f"   - {perm}")
            else:
                for perm in permissions[:5]:
                    print(f"   - {perm}")
                print(f"   ... 还有 {len(permissions) - 5} 个权限")
        
        await db.close()
        return True
        
    except Exception as e:
        logger.error(f"检查权限配置异常: {e}")
        print(f"❌ 检查权限配置异常: {e}")
        return False

async def assign_user_roles():
    """分配用户角色示例"""
    try:
        # 获取数据库会话
        db_gen = get_db()
        db: AsyncSession = next(db_gen)
        
        logger.info("开始分配用户角色...")
        
        # 示例：为用户ID为2的用户分配项目管理员角色
        success = await PermissionService.assign_user_role(db, 2, 'project_manager')
        if success:
            print("✅ 用户角色分配示例完成")
        else:
            print("❌ 用户角色分配示例失败")
        
        await db.close()
        return success
        
    except Exception as e:
        logger.error(f"分配用户角色异常: {e}")
        print(f"❌ 分配用户角色异常: {e}")
        return False

def print_usage():
    """打印使用说明"""
    print("""
🛠️  权限管理初始化工具

使用方法:
    python utils/init_permissions.py [命令]

可用命令:
    init     - 初始化所有角色权限
    check    - 检查权限配置
    assign   - 分配用户角色示例
    all      - 执行所有操作

示例:
    python utils/init_permissions.py init
    python utils/init_permissions.py all
    """)

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print_usage()
        return
    
    command = sys.argv[1].lower()
    
    print("🚀 AI平台权限管理初始化工具")
    print("=" * 50)
    
    if command == 'init':
        await init_role_permissions()
    elif command == 'check':
        await check_permissions()
    elif command == 'assign':
        await assign_user_roles()
    elif command == 'all':
        print("📝 执行完整初始化流程...\n")
        
        # 1. 检查权限配置
        print("1️⃣ 检查权限配置")
        await check_permissions()
        
        # 2. 初始化角色权限
        print("\n2️⃣ 初始化角色权限")
        success = await init_role_permissions()
        
        if success:
            # 3. 分配用户角色示例
            print("\n3️⃣ 分配用户角色示例")
            await assign_user_roles()
            
            print("\n🎉 权限管理系统初始化完成！")
            print("\n📋 后续操作:")
            print("1. 执行 SQL 脚本: ai-platform-backend/sql/insert_new_roles.sql")
            print("2. 重启应用服务")
            print("3. 验证权限功能")
        else:
            print("\n❌ 初始化失败，请检查错误日志")
    else:
        print(f"❌ 未知命令: {command}")
        print_usage()

if __name__ == "__main__":
    # 运行初始化
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行异常: {e}")
        logger.error(f"权限初始化工具运行异常: {e}")